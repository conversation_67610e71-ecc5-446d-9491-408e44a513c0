name: Zero-Downtime Deploy to GCE

on:
  push:
    branches: [main]
  workflow_dispatch:  # Allow manual triggers
    inputs:
      setup_ssl:
        description: 'Setup SSL certificates'
        required: false
        default: 'false'
        type: boolean
      force_ssl_renewal:
        description: 'Force SSL certificate renewal'
        required: false
        default: 'false'
        type: boolean
      force_rollback:
        description: 'Force rollback to previous environment'
        required: false
        default: 'false'
        type: boolean
      skip_health_checks:
        description: 'Skip health checks (not recommended)'
        required: false
        default: 'false'
        type: boolean

jobs:
  zero-downtime-deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 120  # Extended for .NET 10 builds (2 hours)

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3.11.1
      with:
        driver-opts: |
          network=host

    - name: Update Docker Compose
      run: |
        # Ensure we have a recent version of Docker Compose
        sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
        docker-compose --version || echo "docker-compose not available, using docker compose"

    - name: Zero-Downtime Deploy to VM via SSH
      uses: appleboy/ssh-action@v1.2.2
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: 22
        timeout: 7200s  # 2 hours for .NET 10 builds
        command_timeout: 7200s  # 2 hours for .NET 10 builds
        script: |
          set -e  # Exit on any error

          echo "🚀 Starting zero-downtime deployment process..."
          cd ~/abraapi

          # Handle rollback request
          if [ "${{ github.event.inputs.force_rollback }}" = "true" ]; then
            echo "🔄 Rollback requested, executing rollback..."
            chmod +x rollback-recovery.sh
            ./rollback-recovery.sh rollback
            echo "✅ Rollback completed successfully!"
            exit 0
          fi

          # Quick git update
          echo "📥 Updating repository..."
          git fetch origin --depth=1
          git reset --hard origin/main
          echo "✅ Updated to commit: $(git rev-parse --short HEAD)"

          # Verify directory structure
          echo "📁 Verifying project structure..."
          ls -la
          echo "📁 AuthService directory:"
          ls -la AuthService/ || echo "AuthService directory not found"

          # Make deployment scripts executable
          echo "🔧 Setting up deployment scripts..."
          chmod +x deploy-zero-downtime.sh
          chmod +x health-monitor.sh
          chmod +x rollback-recovery.sh

          # Create optimized .env file
          echo "🔧 Creating environment configuration..."
          cat > .env << EOF
          # Database
          POSTGRES_USER=${{ secrets.POSTGRES_USER }}
          POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}
          POSTGRES_DB=${{ secrets.POSTGRES_DB }}
          REDIS_CONNECTION_STRING=${{ secrets.REDIS_CONNECTION_STRING }}

          # APIs
          FINNHUB_API_KEY=${{ secrets.FINNHUB_API_KEY }}
          POLYGON_API_KEY=${{ secrets.POLYGON_API_KEY }}
          ASPNETCORE_ENVIRONMENT=${{ secrets.ASPNETCORE_ENVIRONMENT }}

          # Supabase
          SUPABASE_JWT_SECRET=${{ secrets.SUPABASE_JWT_SECRET }}
          SUPABASE_URL=${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          SUPABASE_PROJECT_ID=${{ secrets.SUPABASE_PROJECT_ID }}
          SUPABASE_CONNECTION_STRING=${{ secrets.SUPABASE_CONNECTION_STRING }}

          # Ollama
          OLLAMA_URL=${{ secrets.OLLAMA_URL }}

          # SSL Configuration
          SSL_EMAIL=${{ secrets.SSL_EMAIL }}
          DOMAIN=abraapp.undeclab.com
          EOF
          
          # Debug: Check if critical environment variables are set
          echo "🔍 Checking critical environment variables..."
          if [ -z "${{ secrets.SUPABASE_CONNECTION_STRING }}" ]; then
            echo "⚠️ WARNING: SUPABASE_CONNECTION_STRING secret is not set!"
          else
            echo "✅ SUPABASE_CONNECTION_STRING is configured"
          fi

          if [ -z "${{ secrets.SUPABASE_URL }}" ]; then
            echo "⚠️ WARNING: SUPABASE_URL secret is not set!"
          else
            echo "✅ SUPABASE_URL is configured"
          fi

          if [ -z "${{ secrets.SUPABASE_JWT_SECRET }}" ]; then
            echo "⚠️ WARNING: SUPABASE_JWT_SECRET secret is not set!"
          else
            echo "✅ SUPABASE_JWT_SECRET is configured"
          fi
          
          # Show .env file structure (without sensitive values)
          echo "📄 Environment file structure:"
          grep -E "^[A-Z_]+" .env | sed 's/=.*/=***/' || echo "No environment variables found"

          # Ensure required directories exist
          mkdir -p nginx
          mkdir -p certbot/www
          mkdir -p certbot/conf
          mkdir -p backups

          # Setup SSL if requested or if certificates don't exist
          SSL_SETUP_NEEDED=false
          if [ "${{ github.event.inputs.setup_ssl }}" = "true" ] || [ "${{ github.event.inputs.force_ssl_renewal }}" = "true" ]; then
            SSL_SETUP_NEEDED=true
          elif [ ! -f "certbot/conf/live/abraapp.undeclab.com/fullchain.pem" ]; then
            echo "🔍 SSL certificates not found, will setup SSL"
            SSL_SETUP_NEEDED=true
          fi

          # Configure SSL setup script
          if [ "$SSL_SETUP_NEEDED" = "true" ]; then
            echo "🔐 Configuring SSL setup..."

            # Update SSL email in setup script
            if [ -n "${{ secrets.SSL_EMAIL }}" ]; then
              sed -i 's/EMAIL="<EMAIL>"/EMAIL="${{ secrets.SSL_EMAIL }}"/' setup-ssl.sh
              echo "✅ SSL email configured"
            else
              echo "⚠️ WARNING: SSL_EMAIL secret not set, using default"
            fi

            # Make SSL script executable
            chmod +x setup-ssl.sh
          fi

          # Initialize nginx configuration for blue-green deployment
          echo "🔧 Setting up nginx for blue-green deployment..."
          if [ ! -f "nginx/upstreams.conf" ]; then
            # Default to blue environment if no current environment is set
            cp nginx/upstreams.blue.conf nginx/upstreams.conf 2>/dev/null || echo "blue" > .current_environment
          fi

          # Enable Docker BuildKit for faster builds
          export DOCKER_BUILDKIT=1
          export COMPOSE_DOCKER_CLI_BUILD=1

          # Check system resources
          echo "💾 System resources:"
          df -h
          free -h

          # Check Docker status
          echo "🐳 Docker status:"
          docker --version
          docker compose version

          # Create backup of current state before deployment
          echo "💾 Creating backup of current state..."
          ./rollback-recovery.sh backup || echo "⚠️ Backup creation failed, continuing..."

          # Handle SSL setup if needed (before main deployment)
          if [ "$SSL_SETUP_NEEDED" = "true" ]; then
            echo "🔐 Setting up SSL certificates..."

            # Use blue-green nginx configuration for SSL setup
            cp nginx/nginx.blue-green.conf nginx/nginx.conf

            # Start nginx with current environment for Let's Encrypt validation
            CURRENT_ENV=$(cat .current_environment 2>/dev/null || echo "blue")
            docker compose -f "docker-compose.$CURRENT_ENV.yml" up -d nginx || true
            sleep 30

            # Setup or renew SSL certificates
            if [ "${{ github.event.inputs.force_ssl_renewal }}" = "true" ]; then
              echo "🔄 Forcing SSL certificate renewal..."
              ./setup-ssl.sh renew || echo "⚠️ SSL renewal failed, continuing with existing certificates"
            else
              echo "🆕 Setting up new SSL certificates..."
              ./setup-ssl.sh setup || echo "⚠️ SSL setup failed, falling back to HTTP"
            fi

            # Restart nginx to load certificates
            docker compose -f "docker-compose.$CURRENT_ENV.yml" restart nginx || true
            sleep 10
          fi

          # Execute zero-downtime deployment
          echo "🚀 Executing zero-downtime deployment..."
          if ./deploy-zero-downtime.sh deploy; then
            echo "✅ Zero-downtime deployment completed successfully!"
          else
            echo "❌ Zero-downtime deployment failed!"

            # Attempt automatic recovery if health checks are not skipped
            if [ "${{ github.event.inputs.skip_health_checks }}" != "true" ]; then
              echo "🔄 Attempting automatic recovery..."
              if ./rollback-recovery.sh auto 2; then
                echo "✅ Automatic recovery successful"
              else
                echo "❌ Automatic recovery failed"
                exit 1
              fi
            else
              exit 1
            fi
          fi

          # Verify deployment health (unless skipped)
          if [ "${{ github.event.inputs.skip_health_checks }}" != "true" ]; then
            echo "🔍 Performing post-deployment health checks..."
            if ./health-monitor.sh check; then
              echo "✅ All health checks passed"
            else
              echo "⚠️ Some health checks failed, but deployment completed"
            fi
          else
            echo "⚠️ Health checks skipped as requested"
          fi

          # Final API connectivity test
          echo "🌐 Testing API connectivity..."
          sleep 10

          # Test main endpoints
          if curl -f -s "https://abraapp.undeclab.com/health" >/dev/null 2>&1; then
            echo "✅ HTTPS health endpoint responding"
          elif curl -f -s "http://abraapp.undeclab.com/health" >/dev/null 2>&1; then
            echo "✅ HTTP health endpoint responding"
          else
            echo "⚠️ API health endpoint not responding (may still be starting)"
          fi

          # Show deployment status
          echo "📊 Final deployment status:"
          ./deploy-zero-downtime.sh status

          # Show current environment
          CURRENT_ENV=$(cat .current_environment 2>/dev/null || echo "unknown")
          echo "🌟 Active environment: $CURRENT_ENV"

          echo "✅ Zero-downtime deployment process completed!"

          # Show final status
          if [ -f "certbot/conf/live/abraapp.undeclab.com/fullchain.pem" ]; then
            echo "🌐 Your API is available at:"
            echo "  - HTTPS: https://abraapp.undeclab.com"
            echo "  - HTTP:  http://abraapp.undeclab.com (redirects to HTTPS)"
          else
            echo "🌐 Your API is available at: http://abraapp.undeclab.com"
          fi

          echo "📋 Deployment completed with zero-downtime blue-green strategy!"
          echo "🔄 To rollback: Run workflow with 'force_rollback' option"
          echo "📊 To monitor: ./health-monitor.sh monitor"