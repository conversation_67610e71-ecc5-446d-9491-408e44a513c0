# Timeout Optimizations for .NET 10 Builds

This document summarizes all timeout optimizations made to accommodate .NET 10 preview build times and startup characteristics.

## Summary of Changes

All timeouts have been significantly increased to accommodate .NET 10 preview's longer build and startup times.

## 🚀 GitHub Actions Workflow

**File**: `.github/workflows/ci-cd.yml`

### Before:
```yaml
timeout-minutes: 45  # 45 minutes
timeout: 2700s       # 45 minutes
command_timeout: 2700s
```

### After:
```yaml
timeout-minutes: 120  # 2 hours
timeout: 7200s        # 2 hours  
command_timeout: 7200s # 2 hours
```

**Rationale**: .NET 10 builds can take 20-30 minutes per service, so 2 hours allows for complete builds of all services plus deployment.

## 🔧 Deployment Scripts

**File**: `deploy-zero-downtime.sh`

### Before:
```bash
HEALTH_CHECK_TIMEOUT=300  # 5 minutes
HEALTH_CHECK_INTERVAL=10  # 10 seconds
sleep 60                  # 1 minute wait
```

### After:
```bash
HEALTH_CHECK_TIMEOUT=1800  # 30 minutes
HEALTH_CHECK_INTERVAL=15   # 15 seconds
sleep 180                  # 3 minutes wait
```

**Rationale**: .NET 10 services take longer to start up and become healthy.

## 🏥 Health Monitoring

**File**: `health-monitor.sh`

### Before:
```bash
# No timeout for port checks
curl -s -w "HTTPSTATUS:%{http_code}" "http://localhost:$port$endpoint"
```

### After:
```bash
timeout 60 bash -c "until nc -z localhost $port; do sleep 2; done"
curl -s -w "HTTPSTATUS:%{http_code}" --max-time 60 "http://localhost:$port$endpoint"
```

**Rationale**: Added explicit timeouts for port availability and HTTP requests.

## 🐳 Docker Compose Health Checks

**Files**: `docker-compose.yml`, `docker-compose.blue.yml`, `docker-compose.green.yml`

### Before (.NET Services):
```yaml
healthcheck:
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s
```

### After (.NET Services):
```yaml
healthcheck:
  interval: 45s      # Less frequent checks
  timeout: 30s       # Longer timeout per check
  retries: 5         # More retries
  start_period: 180s # 3 minutes startup time
```

### Thread Service (Special Case):
```yaml
healthcheck:
  interval: 45s
  timeout: 30s
  retries: 5
  start_period: 300s # 5 minutes (longest startup)
```

**Rationale**: .NET 10 services need more time to:
- Initialize the runtime
- Load assemblies
- Perform JIT compilation
- Start HTTP listeners

### Database Services (Unchanged):
```yaml
# PostgreSQL and Redis timeouts remain the same
# as they start quickly regardless of .NET version
```

## 📊 Timeout Comparison Table

| Component | Old Timeout | New Timeout | Increase |
|-----------|-------------|-------------|----------|
| GitHub Actions Job | 45 min | 120 min | +167% |
| SSH Command | 45 min | 120 min | +167% |
| Health Check Total | 5 min | 30 min | +500% |
| Service Startup | 60s | 180s | +200% |
| Thread Service | 120s | 300s | +150% |
| HTTP Requests | No limit | 60s | New |
| Port Checks | No limit | 60s | New |

## 🎯 Expected Build Timeline

### First Build (Cold Cache):
```
Phase 1: Git Update           ~30 seconds
Phase 2: Environment Setup    ~60 seconds  
Phase 3: Docker Build         ~60-90 minutes (4 services)
Phase 4: Health Checks        ~5-10 minutes
Phase 5: Traffic Switch       ~30 seconds
Total:                        ~70-105 minutes
```

### Subsequent Builds (Warm Cache):
```
Phase 1: Git Update           ~30 seconds
Phase 2: Environment Setup    ~30 seconds
Phase 3: Docker Build         ~10-20 minutes (cached layers)
Phase 4: Health Checks        ~3-5 minutes
Phase 5: Traffic Switch       ~30 seconds
Total:                        ~15-25 minutes
```

## 🔍 Monitoring Recommendations

### During Builds:
1. **Monitor System Resources**:
   ```bash
   # Check memory usage
   free -h
   
   # Check disk space
   df -h
   
   # Monitor build progress
   docker stats
   ```

2. **Watch for Bottlenecks**:
   - Memory swapping (major slowdown)
   - Disk space exhaustion
   - Network bandwidth limits

### During Deployment:
1. **Health Check Logs**:
   ```bash
   # Monitor health check progress
   ./health-monitor.sh monitor
   
   # Check specific service
   curl -f http://localhost:8081/health
   ```

2. **Service Startup**:
   ```bash
   # Watch container logs
   docker compose logs -f assistant-service-green
   ```

## 🚨 Troubleshooting

### If Builds Still Timeout:
1. **Check VM Resources**:
   - Upgrade to higher CPU/memory VM temporarily
   - Ensure sufficient disk space (>10GB free)

2. **Build Optimization**:
   ```bash
   # Use parallel builds
   export DOCKER_BUILDKIT=1
   docker compose build --parallel
   ```

3. **Manual Intervention**:
   ```bash
   # Build services individually if needed
   docker compose build assistant-service-green
   docker compose build auth-service-green
   # etc.
   ```

### If Health Checks Fail:
1. **Check Service Logs**:
   ```bash
   docker compose logs service-name
   ```

2. **Manual Health Check**:
   ```bash
   curl -v http://localhost:8081/health
   ```

3. **Increase Timeouts Further** (if needed):
   - Edit `start_period` in docker-compose files
   - Increase `HEALTH_CHECK_TIMEOUT` in deployment script

## 🎯 Performance Expectations

### Normal Behavior:
- ✅ First build: 60-90 minutes
- ✅ Subsequent builds: 15-25 minutes  
- ✅ Service startup: 2-5 minutes
- ✅ Health checks: 3-10 minutes

### Warning Signs:
- ⚠️ Builds over 2 hours
- ⚠️ Services not healthy after 10 minutes
- ⚠️ Memory usage >90%
- ⚠️ Disk usage >90%

### Critical Issues:
- 🚨 Builds timing out at 2 hours
- 🚨 Services crashing during startup
- 🚨 Out of memory/disk space errors

## 📝 Notes

1. **Preview Version**: These timeouts are optimized for .NET 10 preview. When .NET 10 stable is released, timeouts can likely be reduced.

2. **Resource Dependent**: Actual times depend heavily on VM specifications and network speed.

3. **Cache Benefits**: After the first successful build, subsequent builds will be much faster due to Docker layer caching.

4. **Monitoring**: Use the health monitoring tools to track actual performance and adjust timeouts if needed.
