#!/bin/bash

# Zero-downtime deployment script for Abra API
# Implements blue-green deployment strategy

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NGINX_DIR="$SCRIPT_DIR/nginx"
CURRENT_ENV_FILE="$SCRIPT_DIR/.current_environment"
DEPLOYMENT_LOG="$SCRIPT_DIR/deployment.log"
HEALTH_CHECK_TIMEOUT=1800  # 30 minutes for .NET 10 startup
HEALTH_CHECK_INTERVAL=15   # 15 seconds (less frequent checks)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$DEPLOYMENT_LOG"
}

# Get current active environment
get_current_environment() {
    if [[ -f "$CURRENT_ENV_FILE" ]]; then
        cat "$CURRENT_ENV_FILE"
    else
        echo "blue"  # Default to blue
    fi
}

# Get target environment (opposite of current)
get_target_environment() {
    local current=$(get_current_environment)
    if [[ "$current" == "blue" ]]; then
        echo "green"
    else
        echo "blue"
    fi
}

# Health check function
health_check() {
    local environment=$1
    local service=$2
    local port=$3
    
    log "Health checking $service in $environment environment (port $port)..."
    
    local start_time=$(date +%s)
    local end_time=$((start_time + HEALTH_CHECK_TIMEOUT))
    
    while [[ $(date +%s) -lt $end_time ]]; do
        if curl -f -s "http://localhost:$port/health" > /dev/null 2>&1; then
            success "$service is healthy in $environment environment"
            return 0
        fi
        
        log "Waiting for $service to be healthy... ($(( ($(date +%s) - start_time) ))s elapsed)"
        sleep $HEALTH_CHECK_INTERVAL
    done
    
    error "$service failed health check in $environment environment after ${HEALTH_CHECK_TIMEOUT}s"
    return 1
}

# Comprehensive health check for all services
check_all_services() {
    local environment=$1
    log "Performing comprehensive health check for $environment environment..."
    
    local base_port
    if [[ "$environment" == "blue" ]]; then
        base_port=8081
    else
        base_port=8091
    fi
    
    # Check each service
    health_check "$environment" "assistant-service" "$base_port" || return 1
    health_check "$environment" "auth-service" "$((base_port + 1))" || return 1
    health_check "$environment" "marketdata-service" "$((base_port + 2))" || return 1
    health_check "$environment" "thread-service" "$((base_port + 3))" || return 1
    
    success "All services are healthy in $environment environment"
    return 0
}

# Switch nginx upstream configuration
switch_nginx_upstream() {
    local target_env=$1
    log "Switching nginx upstream to $target_env environment..."
    
    # Copy the appropriate upstream configuration
    cp "$NGINX_DIR/upstreams.$target_env.conf" "$NGINX_DIR/upstreams.conf"
    
    # Test nginx configuration
    if docker exec nginx-proxy nginx -t; then
        # Reload nginx configuration
        docker exec nginx-proxy nginx -s reload
        success "Nginx configuration switched to $target_env environment"
        return 0
    else
        error "Nginx configuration test failed"
        return 1
    fi
}

# Rollback function
rollback() {
    local current_env=$(get_current_environment)
    local previous_env
    if [[ "$current_env" == "blue" ]]; then
        previous_env="green"
    else
        previous_env="blue"
    fi
    
    warning "Rolling back to $previous_env environment..."
    
    # Switch nginx back
    if switch_nginx_upstream "$previous_env"; then
        echo "$previous_env" > "$CURRENT_ENV_FILE"
        success "Rollback completed successfully"
        return 0
    else
        error "Rollback failed"
        return 1
    fi
}

# Deploy function
deploy() {
    local current_env=$(get_current_environment)
    local target_env=$(get_target_environment)
    
    log "Starting zero-downtime deployment..."
    log "Current environment: $current_env"
    log "Target environment: $target_env"
    
    # Ensure nginx directory exists and has proper permissions
    mkdir -p "$NGINX_DIR"
    
    # Initialize current environment file if it doesn't exist
    if [[ ! -f "$CURRENT_ENV_FILE" ]]; then
        echo "blue" > "$CURRENT_ENV_FILE"
        current_env="blue"
        target_env="green"
    fi
    
    # Set up initial upstream configuration if it doesn't exist
    if [[ ! -f "$NGINX_DIR/upstreams.conf" ]]; then
        cp "$NGINX_DIR/upstreams.$current_env.conf" "$NGINX_DIR/upstreams.conf"
    fi
    
    # Stop target environment services (if running)
    log "Stopping $target_env environment services..."
    docker compose -f "docker-compose.$target_env.yml" down --remove-orphans || true
    
    # Clean up Docker resources
    log "Cleaning up Docker resources..."
    docker system prune -f || true
    
    # Build and start target environment
    log "Building and starting $target_env environment..."
    docker compose -f "docker-compose.$target_env.yml" build --no-cache
    docker compose -f "docker-compose.$target_env.yml" up -d
    
    # Wait for services to start (.NET 10 takes longer)
    log "Waiting for .NET 10 services to initialize..."
    sleep 180  # 3 minutes for .NET 10 startup
    
    # Health check target environment
    if ! check_all_services "$target_env"; then
        error "Health check failed for $target_env environment"
        log "Cleaning up failed deployment..."
        docker compose -f "docker-compose.$target_env.yml" down --remove-orphans || true
        return 1
    fi
    
    # Switch traffic to target environment
    if ! switch_nginx_upstream "$target_env"; then
        error "Failed to switch nginx upstream"
        log "Cleaning up failed deployment..."
        docker compose -f "docker-compose.$target_env.yml" down --remove-orphans || true
        return 1
    fi
    
    # Update current environment
    echo "$target_env" > "$CURRENT_ENV_FILE"
    
    # Wait a bit to ensure traffic is flowing
    log "Verifying traffic switch..."
    sleep 30
    
    # Final health check through nginx
    if curl -f -s "https://abraapp.undeclab.com/health" > /dev/null 2>&1; then
        success "Traffic successfully switched to $target_env environment"
    else
        warning "External health check failed, but deployment may still be successful"
    fi
    
    # Clean up old environment
    log "Cleaning up $current_env environment..."
    docker compose -f "docker-compose.$current_env.yml" down --remove-orphans || true
    
    success "Zero-downtime deployment completed successfully!"
    log "Active environment: $target_env"
    
    return 0
}

# Status function
status() {
    local current_env=$(get_current_environment)
    echo "Current active environment: $current_env"
    
    # Check if services are running
    echo "Service status:"
    docker compose -f "docker-compose.$current_env.yml" ps
}

# Main script logic
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "status")
        status
        ;;
    "rollback")
        rollback
        ;;
    "health-check")
        env=${2:-$(get_current_environment)}
        check_all_services "$env"
        ;;
    *)
        echo "Usage: $0 {deploy|status|rollback|health-check [environment]}"
        echo "  deploy      - Perform zero-downtime deployment"
        echo "  status      - Show current deployment status"
        echo "  rollback    - Rollback to previous environment"
        echo "  health-check - Check health of services (optionally specify environment)"
        exit 1
        ;;
esac
