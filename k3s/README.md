# K3s Modular Deployment

This directory contains the modularized K3s deployment configuration for the AbraAPI microservices.

## Structure

```
k3s/
├── services/                    # Individual service configurations
│   ├── assistant-service/       # Assistant service K3s manifests
│   ├── auth-service/           # Auth service K3s manifests
│   ├── marketdata-service/     # Market data service K3s manifests
│   └── thread-service/         # Thread service K3s manifests
├── infrastructure/             # Shared infrastructure components
│   ├── postgres/              # PostgreSQL deployment
│   ├── redis/                 # Redis deployment
│   ├── nginx/                 # Nginx ingress controller
│   └── monitoring/            # Monitoring stack
├── docker-compose/            # Individual Docker Compose files
│   ├── assistant-service.yml  # Assistant service compose
│   ├── auth-service.yml       # Auth service compose
│   ├── marketdata-service.yml # Market data service compose
│   └── thread-service.yml     # Thread service compose
├── ci-cd/                     # Individual CI/CD pipelines
│   ├── assistant-service.yml  # Assistant service pipeline
│   ├── auth-service.yml       # Auth service pipeline
│   ├── marketdata-service.yml # Market data service pipeline
│   └── thread-service.yml     # Thread service pipeline
└── scripts/                   # Deployment and management scripts
    ├── deploy-service.sh      # Deploy individual service
    ├── setup-k3s.sh          # K3s cluster setup
    └── manage-cluster.sh      # Cluster management utilities
```

## Services Overview

### Assistant Service
- **Dependencies**: None (external Ollama API)
- **Port**: 8080
- **Health Check**: `/health`
- **Environment**: OLLAMA_URL

### Auth Service
- **Dependencies**: None (external Supabase)
- **Port**: 8080
- **Health Check**: `/health`
- **Environment**: Supabase configuration

### Market Data Service
- **Dependencies**: PostgreSQL, Redis
- **Port**: 8080
- **Health Check**: `/health`
- **Environment**: Database connections, API keys

### Thread Service
- **Dependencies**: PostgreSQL (shared with MarketData)
- **Port**: 8080
- **Health Check**: `/health/ready`
- **Environment**: Database connections

## Quick Start

1. **Setup K3s cluster**:
   ```bash
   ./scripts/setup-k3s.sh
   ```

2. **Deploy infrastructure**:
   ```bash
   kubectl apply -f infrastructure/
   ```

3. **Deploy individual services**:
   ```bash
   ./scripts/deploy-service.sh assistant-service
   ./scripts/deploy-service.sh auth-service
   ./scripts/deploy-service.sh marketdata-service
   ./scripts/deploy-service.sh thread-service
   ```

## Development

Each service can be developed and deployed independently using its own Docker Compose file:

```bash
# Develop assistant service locally
cd docker-compose/
docker-compose -f assistant-service.yml up --build

# Deploy to K3s
./scripts/deploy-service.sh assistant-service
```
