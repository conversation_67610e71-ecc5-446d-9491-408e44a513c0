name: MarketData Service - K3s Deploy

on:
  push:
    branches: [main]
    paths:
      - 'MarketDataService/**'
      - 'k3s/services/marketdata-service/**'
      - 'k3s/docker-compose/marketdata-service.yml'
  pull_request:
    branches: [main]
    paths:
      - 'MarketDataService/**'
      - 'k3s/services/marketdata-service/**'
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if no changes detected'
        required: false
        default: 'false'
        type: boolean
      environment:
        description: 'Target environment'
        required: false
        default: 'production'
        type: choice
        options:
        - development
        - staging
        - production

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: marketdata-service
  K3S_NAMESPACE: abraapi

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '10.0.x'
        include-prerelease: true

    - name: Restore dependencies
      run: dotnet restore MarketDataService/MarketDataService.csproj

    - name: Build
      run: dotnet build MarketDataService/MarketDataService.csproj --no-restore

    - name: Test
      run: dotnet test tests/marketdata-service.Tests/ --no-build --verbosity normal
      env:
        ConnectionStrings__DefaultConnection: "Host=localhost;Port=5432;Database=test_db;Username=test;Password=test"
        Redis__ConnectionString: "localhost:6379"

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event.inputs.force_deploy == 'true'
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository_owner }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: ./MarketDataService
        file: ./MarketDataService/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event.inputs.force_deploy == 'true'
    environment: ${{ github.event.inputs.environment || 'production' }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'

    - name: Configure kubectl
      run: |
        mkdir -p ~/.kube
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > ~/.kube/config
        chmod 600 ~/.kube/config

    - name: Ensure infrastructure is ready
      run: |
        # Check if PostgreSQL and Redis are running
        kubectl get pods -n abraapi-infrastructure -l app=postgres
        kubectl get pods -n abraapi-infrastructure -l app=redis
        
        # Wait for infrastructure to be ready
        kubectl wait --for=condition=ready pod -l app=postgres -n abraapi-infrastructure --timeout=300s
        kubectl wait --for=condition=ready pod -l app=redis -n abraapi-infrastructure --timeout=300s

    - name: Update deployment image
      run: |
        # Update the image in the deployment manifest
        sed -i "s|image: marketdata-service:latest|image: ${{ needs.build-and-push.outputs.image-tag }}|g" \
          k3s/services/marketdata-service/deployment.yml

    - name: Deploy to K3s
      run: |
        # Apply the updated deployment
        kubectl apply -f k3s/services/marketdata-service/deployment.yml
        
        # Wait for rollout to complete
        kubectl rollout status deployment/marketdata-service -n ${{ env.K3S_NAMESPACE }} --timeout=600s

    - name: Verify deployment
      run: |
        # Check if pods are running
        kubectl get pods -n ${{ env.K3S_NAMESPACE }} -l app=marketdata-service
        
        # Check service endpoints
        kubectl get endpoints -n ${{ env.K3S_NAMESPACE }} marketdata-service
        
        # Test health endpoint (if accessible)
        kubectl run test-pod --rm -i --restart=Never --image=curlimages/curl -- \
          curl -f http://marketdata-service.${{ env.K3S_NAMESPACE }}.svc.cluster.local:8080/health

    - name: Rollback on failure
      if: failure()
      run: |
        echo "Deployment failed, rolling back..."
        kubectl rollout undo deployment/marketdata-service -n ${{ env.K3S_NAMESPACE }}
        kubectl rollout status deployment/marketdata-service -n ${{ env.K3S_NAMESPACE }} --timeout=300s
