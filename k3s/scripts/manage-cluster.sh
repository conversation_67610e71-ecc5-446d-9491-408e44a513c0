#!/bin/bash

# K3s Cluster Management Script
# This script provides utilities for managing the K3s cluster

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
K3S_DIR="$(dirname "$SCRIPT_DIR")"

# Show cluster status
show_status() {
    log_info "=== K3s Cluster Status ==="
    
    # Check K3s service
    if sudo systemctl is-active --quiet k3s; then
        log_success "K3s service is running"
    else
        log_error "K3s service is not running"
        return 1
    fi
    
    # Show cluster info
    echo ""
    log_info "Cluster Information:"
    kubectl cluster-info
    
    # Show nodes
    echo ""
    log_info "Nodes:"
    kubectl get nodes -o wide
    
    # Show namespaces
    echo ""
    log_info "Namespaces:"
    kubectl get namespaces
    
    # Show infrastructure status
    echo ""
    log_info "Infrastructure Status:"
    kubectl get pods -n abraapi-infrastructure -o wide 2>/dev/null || log_warning "Infrastructure namespace not found"
    
    # Show application services status
    echo ""
    log_info "Application Services Status:"
    kubectl get pods -n abraapi -o wide 2>/dev/null || log_warning "Application namespace not found"
    
    # Show ingress status
    echo ""
    log_info "Ingress Status:"
    kubectl get ingress -n abraapi 2>/dev/null || log_warning "No ingress found"
    
    # Show persistent volumes
    echo ""
    log_info "Persistent Volumes:"
    kubectl get pv,pvc --all-namespaces
}

# Show logs for a service
show_logs() {
    local service=$1
    local namespace=${2:-abraapi}
    local lines=${3:-100}
    
    if [[ -z "$service" ]]; then
        log_error "Service name is required"
        return 1
    fi
    
    log_info "Showing logs for '$service' in namespace '$namespace' (last $lines lines):"
    kubectl logs -n "$namespace" -l app="$service" --tail="$lines" --follow=false
}

# Scale a service
scale_service() {
    local service=$1
    local replicas=$2
    local namespace=${3:-abraapi}
    
    if [[ -z "$service" || -z "$replicas" ]]; then
        log_error "Service name and replica count are required"
        return 1
    fi
    
    log_info "Scaling '$service' to $replicas replicas in namespace '$namespace'..."
    kubectl scale deployment "$service" --replicas="$replicas" -n "$namespace"
    
    # Wait for scaling to complete
    kubectl rollout status deployment/"$service" -n "$namespace" --timeout=300s
    
    log_success "Service '$service' scaled to $replicas replicas"
}

# Restart a service
restart_service() {
    local service=$1
    local namespace=${2:-abraapi}
    
    if [[ -z "$service" ]]; then
        log_error "Service name is required"
        return 1
    fi
    
    log_info "Restarting '$service' in namespace '$namespace'..."
    kubectl rollout restart deployment/"$service" -n "$namespace"
    
    # Wait for restart to complete
    kubectl rollout status deployment/"$service" -n "$namespace" --timeout=600s
    
    log_success "Service '$service' restarted successfully"
}

# Delete a service
delete_service() {
    local service=$1
    local namespace=${2:-abraapi}
    local confirm=${3:-false}
    
    if [[ -z "$service" ]]; then
        log_error "Service name is required"
        return 1
    fi
    
    if [[ "$confirm" != "true" ]]; then
        log_warning "This will delete the service '$service' and all its resources."
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Operation cancelled"
            return 0
        fi
    fi
    
    log_info "Deleting service '$service' from namespace '$namespace'..."
    
    # Delete deployment
    kubectl delete deployment "$service" -n "$namespace" --ignore-not-found=true
    
    # Delete service
    kubectl delete service "$service" -n "$namespace" --ignore-not-found=true
    
    # Delete HPA if exists
    kubectl delete hpa "${service}-hpa" -n "$namespace" --ignore-not-found=true
    
    # Delete configmaps and secrets
    kubectl delete configmap "${service}-config" -n "$namespace" --ignore-not-found=true
    kubectl delete secret "${service}-secret" -n "$namespace" --ignore-not-found=true
    
    log_success "Service '$service' deleted successfully"
}

# Clean up cluster resources
cleanup() {
    local force=${1:-false}
    
    if [[ "$force" != "true" ]]; then
        log_warning "This will clean up unused resources in the cluster."
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Operation cancelled"
            return 0
        fi
    fi
    
    log_info "Cleaning up cluster resources..."
    
    # Clean up completed pods
    kubectl delete pods --field-selector=status.phase=Succeeded --all-namespaces --ignore-not-found=true
    
    # Clean up failed pods
    kubectl delete pods --field-selector=status.phase=Failed --all-namespaces --ignore-not-found=true
    
    # Clean up evicted pods
    kubectl get pods --all-namespaces | grep Evicted | awk '{print $1, $2}' | xargs -n2 kubectl delete pod -n 2>/dev/null || true
    
    log_success "Cluster cleanup completed"
}

# Backup cluster configuration
backup() {
    local backup_dir=${1:-"./backups/$(date +%Y%m%d_%H%M%S)"}
    
    log_info "Creating cluster backup in '$backup_dir'..."
    
    mkdir -p "$backup_dir"
    
    # Backup all resources
    kubectl get all --all-namespaces -o yaml > "$backup_dir/all-resources.yaml"
    
    # Backup configmaps and secrets
    kubectl get configmaps --all-namespaces -o yaml > "$backup_dir/configmaps.yaml"
    kubectl get secrets --all-namespaces -o yaml > "$backup_dir/secrets.yaml"
    
    # Backup persistent volumes
    kubectl get pv,pvc --all-namespaces -o yaml > "$backup_dir/volumes.yaml"
    
    # Backup ingress
    kubectl get ingress --all-namespaces -o yaml > "$backup_dir/ingress.yaml"
    
    # Copy K3s config
    sudo cp /etc/rancher/k3s/k3s.yaml "$backup_dir/k3s-config.yaml" 2>/dev/null || log_warning "Could not backup K3s config"
    
    log_success "Cluster backup created in '$backup_dir'"
}

# Monitor cluster resources
monitor() {
    local interval=${1:-5}
    
    log_info "Monitoring cluster resources (refresh every ${interval}s, press Ctrl+C to stop)..."
    
    while true; do
        clear
        echo "=== K3s Cluster Monitor ($(date)) ==="
        echo ""
        
        # Show node resources
        echo "Node Resources:"
        kubectl top nodes 2>/dev/null || echo "Metrics not available"
        echo ""
        
        # Show pod resources
        echo "Pod Resources (abraapi namespace):"
        kubectl top pods -n abraapi 2>/dev/null || echo "Metrics not available"
        echo ""
        
        # Show pod status
        echo "Pod Status:"
        kubectl get pods --all-namespaces | grep -E "(abraapi|traefik|cert-manager)"
        echo ""
        
        # Show events
        echo "Recent Events:"
        kubectl get events --all-namespaces --sort-by='.lastTimestamp' | tail -10
        
        sleep "$interval"
    done
}

# Print usage information
print_usage() {
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  status                          Show cluster status"
    echo "  logs <service> [namespace]      Show logs for a service"
    echo "  scale <service> <replicas>      Scale a service"
    echo "  restart <service>               Restart a service"
    echo "  delete <service>                Delete a service"
    echo "  cleanup                         Clean up unused resources"
    echo "  backup [directory]              Backup cluster configuration"
    echo "  monitor [interval]              Monitor cluster resources"
    echo ""
    echo "Options:"
    echo "  -h, --help                      Show this help message"
    echo "  -f, --force                     Force operation without confirmation"
    echo ""
    echo "Examples:"
    echo "  $0 status                       # Show cluster status"
    echo "  $0 logs assistant-service       # Show logs for assistant service"
    echo "  $0 scale auth-service 3         # Scale auth service to 3 replicas"
    echo "  $0 restart marketdata-service   # Restart market data service"
    echo "  $0 delete thread-service        # Delete thread service"
    echo "  $0 cleanup --force              # Clean up without confirmation"
    echo "  $0 backup ./my-backup           # Backup to specific directory"
    echo "  $0 monitor 10                   # Monitor with 10s refresh interval"
}

# Parse command line arguments
COMMAND=""
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            print_usage
            exit 0
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        status|logs|scale|restart|delete|cleanup|backup|monitor)
            COMMAND="$1"
            shift
            break
            ;;
        *)
            log_error "Unknown command: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Execute command
case $COMMAND in
    status)
        show_status
        ;;
    logs)
        show_logs "$@"
        ;;
    scale)
        scale_service "$@"
        ;;
    restart)
        restart_service "$@"
        ;;
    delete)
        delete_service "$1" "$2" "$FORCE"
        ;;
    cleanup)
        cleanup "$FORCE"
        ;;
    backup)
        backup "$@"
        ;;
    monitor)
        monitor "$@"
        ;;
    "")
        log_error "No command specified"
        print_usage
        exit 1
        ;;
    *)
        log_error "Unknown command: $COMMAND"
        print_usage
        exit 1
        ;;
esac
