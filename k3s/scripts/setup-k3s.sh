#!/bin/bash

# K3s Cluster Setup Script for AbraAPI
# This script sets up a K3s cluster with all necessary components

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
K3S_VERSION=${K3S_VERSION:-"v1.28.5+k3s1"}
CLUSTER_NAME=${CLUSTER_NAME:-"abraapi-k3s"}
KUBECONFIG_PATH=${KUBECONFIG_PATH:-"$HOME/.kube/config"}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check if curl is installed
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    # Check available memory (K3s needs at least 512MB)
    available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$available_memory" -lt 512 ]; then
        log_warning "Available memory is less than 512MB. K3s may not work properly."
    fi
    
    # Check available disk space (need at least 2GB)
    available_space=$(df / | awk 'NR==2{print $4}')
    if [ "$available_space" -lt 2097152 ]; then  # 2GB in KB
        log_warning "Available disk space is less than 2GB. Consider freeing up space."
    fi
    
    log_success "System requirements check completed"
}

# Install K3s
install_k3s() {
    log_info "Installing K3s version $K3S_VERSION..."
    
    # Check if K3s is already installed
    if command -v k3s &> /dev/null; then
        log_warning "K3s is already installed. Skipping installation."
        return 0
    fi
    
    # Install K3s with specific configuration for AbraAPI
    curl -sfL https://get.k3s.io | INSTALL_K3S_VERSION="$K3S_VERSION" sh -s - \
        --write-kubeconfig-mode 644 \
        --disable traefik \
        --disable servicelb \
        --disable local-storage \
        --cluster-init \
        --node-name "$CLUSTER_NAME-master"
    
    log_success "K3s installed successfully"
}

# Configure kubectl
configure_kubectl() {
    log_info "Configuring kubectl..."
    
    # Create .kube directory if it doesn't exist
    mkdir -p "$(dirname "$KUBECONFIG_PATH")"
    
    # Copy K3s kubeconfig
    sudo cp /etc/rancher/k3s/k3s.yaml "$KUBECONFIG_PATH"
    sudo chown "$USER:$USER" "$KUBECONFIG_PATH"
    chmod 600 "$KUBECONFIG_PATH"
    
    # Set KUBECONFIG environment variable
    export KUBECONFIG="$KUBECONFIG_PATH"
    echo "export KUBECONFIG=$KUBECONFIG_PATH" >> ~/.bashrc
    
    log_success "kubectl configured successfully"
}

# Install Helm
install_helm() {
    log_info "Installing Helm..."
    
    if command -v helm &> /dev/null; then
        log_warning "Helm is already installed. Skipping installation."
        return 0
    fi
    
    curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    
    log_success "Helm installed successfully"
}

# Install cert-manager
install_cert_manager() {
    log_info "Installing cert-manager..."
    
    # Add cert-manager Helm repository
    helm repo add jetstack https://charts.jetstack.io
    helm repo update
    
    # Install cert-manager
    helm install cert-manager jetstack/cert-manager \
        --namespace cert-manager \
        --create-namespace \
        --version v1.13.0 \
        --set installCRDs=true
    
    # Wait for cert-manager to be ready
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=cert-manager -n cert-manager --timeout=300s
    
    log_success "cert-manager installed successfully"
}

# Install Traefik (as ingress controller)
install_traefik() {
    log_info "Installing Traefik ingress controller..."
    
    # Add Traefik Helm repository
    helm repo add traefik https://traefik.github.io/charts
    helm repo update
    
    # Install Traefik
    helm install traefik traefik/traefik \
        --namespace traefik-system \
        --create-namespace \
        --set ports.web.redirectTo=websecure \
        --set ports.websecure.tls.enabled=true \
        --set globalArguments="{--global.sendanonymoususage=false,--global.checknewversion=false}" \
        --set additionalArguments="{--providers.kubernetesingress.ingressendpoint.ip=127.0.0.1}"
    
    # Wait for Traefik to be ready
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=traefik -n traefik-system --timeout=300s
    
    log_success "Traefik installed successfully"
}

# Create namespaces
create_namespaces() {
    log_info "Creating namespaces..."
    
    kubectl apply -f ../infrastructure/namespace.yml
    
    log_success "Namespaces created successfully"
}

# Deploy infrastructure
deploy_infrastructure() {
    log_info "Deploying infrastructure components..."
    
    # Deploy PostgreSQL
    kubectl apply -f ../infrastructure/postgres/postgres.yml
    
    # Deploy Redis
    kubectl apply -f ../infrastructure/redis/redis.yml
    
    # Wait for infrastructure to be ready
    log_info "Waiting for infrastructure to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n abraapi-infrastructure --timeout=300s
    kubectl wait --for=condition=ready pod -l app=redis -n abraapi-infrastructure --timeout=300s
    
    log_success "Infrastructure deployed successfully"
}

# Verify installation
verify_installation() {
    log_info "Verifying K3s installation..."
    
    # Check K3s status
    sudo systemctl is-active --quiet k3s && log_success "K3s service is running" || log_error "K3s service is not running"
    
    # Check kubectl connectivity
    kubectl cluster-info && log_success "kubectl is working" || log_error "kubectl is not working"
    
    # Check nodes
    kubectl get nodes && log_success "Nodes are ready" || log_error "Nodes are not ready"
    
    # Check namespaces
    kubectl get namespaces abraapi abraapi-infrastructure && log_success "Namespaces created" || log_error "Namespaces not found"
    
    # Check infrastructure
    kubectl get pods -n abraapi-infrastructure && log_success "Infrastructure pods are running" || log_warning "Some infrastructure pods may not be ready"
    
    log_success "K3s cluster setup completed successfully!"
}

# Print usage information
print_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -v, --version VERSION   Specify K3s version (default: $K3S_VERSION)"
    echo "  -n, --name NAME         Specify cluster name (default: $CLUSTER_NAME)"
    echo "  -k, --kubeconfig PATH   Specify kubeconfig path (default: $KUBECONFIG_PATH)"
    echo ""
    echo "Environment variables:"
    echo "  K3S_VERSION             K3s version to install"
    echo "  CLUSTER_NAME            Name of the cluster"
    echo "  KUBECONFIG_PATH         Path to kubeconfig file"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            print_usage
            exit 0
            ;;
        -v|--version)
            K3S_VERSION="$2"
            shift 2
            ;;
        -n|--name)
            CLUSTER_NAME="$2"
            shift 2
            ;;
        -k|--kubeconfig)
            KUBECONFIG_PATH="$2"
            shift 2
            ;;
        *)
            log_error "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    log_info "Starting K3s cluster setup for AbraAPI..."
    log_info "K3s Version: $K3S_VERSION"
    log_info "Cluster Name: $CLUSTER_NAME"
    log_info "Kubeconfig Path: $KUBECONFIG_PATH"
    
    check_root
    check_requirements
    install_k3s
    configure_kubectl
    install_helm
    install_cert_manager
    install_traefik
    create_namespaces
    deploy_infrastructure
    verify_installation
    
    log_success "🎉 K3s cluster setup completed successfully!"
    log_info "You can now deploy services using: ./deploy-service.sh <service-name>"
    log_info "Available services: assistant-service, auth-service, marketdata-service, thread-service"
}

# Run main function
main "$@"
